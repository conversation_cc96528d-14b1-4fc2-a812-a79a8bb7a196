"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/warranties/components/page",{

/***/ "(app-pages-browser)/./src/app/warranties/components/page.tsx":
/*!************************************************!*\
  !*** ./src/app/warranties/components/page.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ComponentTrackingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_warranties_warranty_component_form__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/warranties/warranty-component-form */ \"(app-pages-browser)/./src/components/warranties/warranty-component-form.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_13__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Component Tracking Page\n * \n * This page displays and manages individual components and their warranty status.\n * It includes component-level warranty tracking and expiration alerts.\n */ function ComponentTrackingPage() {\n    _s();\n    _s1();\n    const [components, setComponents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [machineFilter, setMachineFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [showAddDialog, setShowAddDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingComponent, setEditingComponent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showEditDialog, setShowEditDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load warranty components from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ComponentTrackingPage.useEffect\": ()=>{\n            const loadComponents = {\n                \"ComponentTrackingPage.useEffect.loadComponents\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        const response = await fetch('/api/warranties/components', {\n                            credentials: 'include',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error('Failed to fetch warranty components');\n                        }\n                        const data = await response.json();\n                        setComponents(data.components || []);\n                        setError(null);\n                    } catch (err) {\n                        console.error('Error loading components:', err);\n                        setError('Failed to load component data');\n                        setComponents([]);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"ComponentTrackingPage.useEffect.loadComponents\"];\n            loadComponents();\n            // Listen for add component event from layout\n            const handleAddComponent = {\n                \"ComponentTrackingPage.useEffect.handleAddComponent\": ()=>{\n                    setShowAddDialog(true);\n                }\n            }[\"ComponentTrackingPage.useEffect.handleAddComponent\"];\n            window.addEventListener('addWarrantyComponent', handleAddComponent);\n            return ({\n                \"ComponentTrackingPage.useEffect\": ()=>window.removeEventListener('addWarrantyComponent', handleAddComponent)\n            })[\"ComponentTrackingPage.useEffect\"];\n        }\n    }[\"ComponentTrackingPage.useEffect\"], []);\n    const getWarrantyStatusBadge = (warrantyDate)=>{\n        const today = new Date();\n        const warranty = new Date(warrantyDate);\n        const daysUntilExpiry = Math.ceil((warranty.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        if (daysUntilExpiry < 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                variant: \"destructive\",\n                className: \"flex items-center space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Expired\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 14\n            }, this);\n        }\n        if (daysUntilExpiry <= 30) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                variant: \"secondary\",\n                className: \"flex items-center space-x-1 bg-yellow-100 text-yellow-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Expiring Soon\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n            variant: \"secondary\",\n            className: \"flex items-center space-x-1 bg-green-100 text-green-800\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: \"Active\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n            lineNumber: 88,\n            columnNumber: 12\n        }, this);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-IN');\n    };\n    const filteredComponents = components.filter((component)=>{\n        const matchesSearch = searchTerm === '' || component.serialNumber.toLowerCase().includes(searchTerm.toLowerCase()) || component.machine.serialNumber.toLowerCase().includes(searchTerm.toLowerCase()) || component.machine.warranty.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) || component.machine.product.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const warrantyDate = new Date(component.warrantyDate);\n        const today = new Date();\n        const isExpired = warrantyDate < today;\n        const isExpiring = !isExpired && warrantyDate <= new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);\n        const isActive = !isExpired && !isExpiring;\n        let matchesStatus = true;\n        if (statusFilter === 'active') matchesStatus = isActive;\n        else if (statusFilter === 'expiring') matchesStatus = isExpiring;\n        else if (statusFilter === 'expired') matchesStatus = isExpired;\n        return matchesSearch && matchesStatus;\n    });\n    const handleExport = async ()=>{\n        try {\n            const response = await fetch('/api/warranties/components/export?format=CSV', {\n                credentials: 'include',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to export component data');\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"warranty-components-\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n        } catch (error) {\n            console.error('Error exporting component data:', error);\n            setError('Failed to export component data');\n        }\n    };\n    // Handle component operations\n    const handleEdit = (component)=>{\n        setEditingComponent(component);\n        setShowEditDialog(true);\n    };\n    const handleDelete = async (componentId)=>{\n        if (!confirm('Are you sure you want to delete this component?')) {\n            return;\n        }\n        try {\n            setDeletingId(componentId);\n            const response = await fetch(\"/api/warranties/components/\".concat(componentId), {\n                method: 'DELETE',\n                credentials: 'include'\n            });\n            if (!response.ok) {\n                throw new Error('Failed to delete component');\n            }\n            // Remove from local state and reload\n            setComponents((prev)=>prev.filter((c)=>c.id !== componentId));\n        } catch (error) {\n            console.error('Error deleting component:', error);\n            setError('Failed to delete component');\n        } finally{\n            setDeletingId(null);\n        }\n    };\n    const handleFormSuccess = ()=>{\n        setShowAddDialog(false);\n        setShowEditDialog(false);\n        setEditingComponent(null);\n        // Reload components\n        window.location.reload();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"pb-3 flex flex-row items-center justify-between bg-primary text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Component Tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"text-gray-100\",\n                                        children: \"Track individual components and their warranty status across all machines\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"secondary\",\n                                        onClick: handleExport,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Export\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"secondary\",\n                                        onClick: ()=>setShowAddDialog(true),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Add Component\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"search\",\n                                                className: \"text-black\",\n                                                children: \"Search\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"search\",\n                                                        placeholder: \"Search by component, machine, or customer...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-10\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"status\",\n                                                className: \"text-black\",\n                                                children: \"Warranty Status\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: statusFilter,\n                                                onValueChange: setStatusFilter,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        id: \"status\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                            placeholder: \"Select status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"all\",\n                                                                children: \"All Statuses\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"active\",\n                                                                children: \"Active\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"expiring\",\n                                                                children: \"Expiring Soon\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"expired\",\n                                                                children: \"Expired\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"machine\",\n                                                className: \"text-black\",\n                                                children: \"Machine\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: machineFilter,\n                                                onValueChange: setMachineFilter,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        id: \"machine\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                            placeholder: \"Select machine\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                            value: \"all\",\n                                                            children: \"All Machines\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                                        className: \"text-black\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        className: \"text-black\",\n                                                        children: \"Component\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        className: \"text-black\",\n                                                        children: \"Machine\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        className: \"text-black\",\n                                                        children: \"Customer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        className: \"text-black\",\n                                                        children: \"Product\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        className: \"text-black\",\n                                                        children: \"Warranty Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        className: \"text-black\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        className: \"text-right text-black\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                            children: isLoading ? // Loading skeleton\n                                            Array.from({\n                                                length: 5\n                                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                className: \"h-6 w-24\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                className: \"h-6 w-32\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                className: \"h-6 w-32\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                className: \"h-6 w-28\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                className: \"h-6 w-24\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                className: \"h-6 w-20\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                className: \"h-6 w-16 ml-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 57\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, \"skeleton-\".concat(index), true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 36\n                                                }, this)) : filteredComponents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    colSpan: 7,\n                                                    className: \"text-center py-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-8 w-8 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500\",\n                                                                children: \"No components found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: ()=>setShowAddDialog(true),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 270,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Add First Component\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 70\n                                            }, this) : filteredComponents.map((component)=>{\n                                                var _component_machine, _component_machine1, _component_machine_warranty_customer, _component_machine_warranty, _component_machine2, _component_machine_warranty_customer1, _component_machine_warranty1, _component_machine3, _component_machine_warranty2, _component_machine4, _component_machine_product, _component_machine5, _component_machine_model, _component_machine6, _component_machine_brand, _component_machine7;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            className: \"text-black\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            \"#\",\n                                                                            component.componentNo\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            \"SN: \",\n                                                                            component.serialNumber\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            \"Section: \",\n                                                                            component.section\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            className: \"text-black\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: ((_component_machine = component.machine) === null || _component_machine === void 0 ? void 0 : _component_machine.serialNumber) || 'Unknown Serial'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 285,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: ((_component_machine1 = component.machine) === null || _component_machine1 === void 0 ? void 0 : _component_machine1.location) || 'Unknown Location'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            className: \"text-black\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: ((_component_machine2 = component.machine) === null || _component_machine2 === void 0 ? void 0 : (_component_machine_warranty = _component_machine2.warranty) === null || _component_machine_warranty === void 0 ? void 0 : (_component_machine_warranty_customer = _component_machine_warranty.customer) === null || _component_machine_warranty_customer === void 0 ? void 0 : _component_machine_warranty_customer.name) || 'Unknown Customer'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: ((_component_machine3 = component.machine) === null || _component_machine3 === void 0 ? void 0 : (_component_machine_warranty1 = _component_machine3.warranty) === null || _component_machine_warranty1 === void 0 ? void 0 : (_component_machine_warranty_customer1 = _component_machine_warranty1.customer) === null || _component_machine_warranty_customer1 === void 0 ? void 0 : _component_machine_warranty_customer1.city) || 'Unknown City'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            \"BSL: \",\n                                                                            ((_component_machine4 = component.machine) === null || _component_machine4 === void 0 ? void 0 : (_component_machine_warranty2 = _component_machine4.warranty) === null || _component_machine_warranty2 === void 0 ? void 0 : _component_machine_warranty2.bslNo) || 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            className: \"text-black\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: ((_component_machine5 = component.machine) === null || _component_machine5 === void 0 ? void 0 : (_component_machine_product = _component_machine5.product) === null || _component_machine_product === void 0 ? void 0 : _component_machine_product.name) || 'Unknown Product'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: ((_component_machine6 = component.machine) === null || _component_machine6 === void 0 ? void 0 : (_component_machine_model = _component_machine6.model) === null || _component_machine_model === void 0 ? void 0 : _component_machine_model.name) || 'Unknown Model'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: ((_component_machine7 = component.machine) === null || _component_machine7 === void 0 ? void 0 : (_component_machine_brand = _component_machine7.brand) === null || _component_machine_brand === void 0 ? void 0 : _component_machine_brand.name) || 'Unknown Brand'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            className: \"text-black\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatDate(component.warrantyDate)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: getWarrantyStatusBadge(component.warrantyDate)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-end space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                                                            href: \"/warranties/components/\".concat(component.id),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                                lineNumber: 314,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                            lineNumber: 313,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>handleEdit(component),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                            lineNumber: 318,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"text-destructive hover:text-destructive\",\n                                                                        onClick: ()=>handleDelete(component.id),\n                                                                        disabled: deletingId === component.id,\n                                                                        children: deletingId === component.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-red-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 60\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 146\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, component.id, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 69\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            !isLoading && filteredComponents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Showing \",\n                                        filteredComponents.length,\n                                        \" of \",\n                                        components.length,\n                                        \" components\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 59\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.Dialog, {\n                open: showAddDialog,\n                onOpenChange: setShowAddDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogContent, {\n                    className: \"max-w-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogTitle, {\n                                className: \"text-black\",\n                                children: \"Add New Component\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_warranties_warranty_component_form__WEBPACK_IMPORTED_MODULE_12__.WarrantyComponentForm, {\n                            onSuccess: handleFormSuccess,\n                            onCancel: ()=>setShowAddDialog(false)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                lineNumber: 341,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.Dialog, {\n                open: showEditDialog,\n                onOpenChange: setShowEditDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogContent, {\n                    className: \"max-w-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogTitle, {\n                                className: \"text-black\",\n                                children: \"Edit Component\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_warranties_warranty_component_form__WEBPACK_IMPORTED_MODULE_12__.WarrantyComponentForm, {\n                            component: editingComponent,\n                            onSuccess: handleFormSuccess,\n                            onCancel: ()=>{\n                                setShowEditDialog(false);\n                                setEditingComponent(null);\n                            }\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                lineNumber: 351,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n        lineNumber: 168,\n        columnNumber: 10\n    }, this);\n}\n_s(ComponentTrackingPage, \"OeE3I1HxYl6xc894kD63MuN0z3k=\");\n_c1 = ComponentTrackingPage;\n_s1(ComponentTrackingPage, \"OeE3I1HxYl6xc894kD63MuN0z3k=\");\n_c = ComponentTrackingPage;\nvar _c;\n$RefreshReg$(_c, \"ComponentTrackingPage\");\nvar _c1;\n$RefreshReg$(_c1, \"ComponentTrackingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/warranties/components/page.tsx\n"));

/***/ })

});