"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/warranties/components/page",{

/***/ "(app-pages-browser)/./src/app/warranties/components/page.tsx":
/*!************************************************!*\
  !*** ./src/app/warranties/components/page.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ComponentTrackingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_12__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Component Tracking Page\n * \n * This page displays and manages individual components and their warranty status.\n * It includes component-level warranty tracking and expiration alerts.\n */ function ComponentTrackingPage() {\n    _s();\n    _s1();\n    const [components, setComponents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [machineFilter, setMachineFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [showAddDialog, setShowAddDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingComponent, setEditingComponent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showEditDialog, setShowEditDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load warranty components from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ComponentTrackingPage.useEffect\": ()=>{\n            const loadComponents = {\n                \"ComponentTrackingPage.useEffect.loadComponents\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        const response = await fetch('/api/warranties/components', {\n                            credentials: 'include',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error('Failed to fetch warranty components');\n                        }\n                        const data = await response.json();\n                        setComponents(data.components || []);\n                        setError(null);\n                    } catch (err) {\n                        console.error('Error loading components:', err);\n                        setError('Failed to load component data');\n                        setComponents([]);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"ComponentTrackingPage.useEffect.loadComponents\"];\n            loadComponents();\n            // Listen for add component event from layout\n            const handleAddComponent = {\n                \"ComponentTrackingPage.useEffect.handleAddComponent\": ()=>{\n                    setShowAddDialog(true);\n                }\n            }[\"ComponentTrackingPage.useEffect.handleAddComponent\"];\n            window.addEventListener('addWarrantyComponent', handleAddComponent);\n            return ({\n                \"ComponentTrackingPage.useEffect\": ()=>window.removeEventListener('addWarrantyComponent', handleAddComponent)\n            })[\"ComponentTrackingPage.useEffect\"];\n        }\n    }[\"ComponentTrackingPage.useEffect\"], []);\n    const getWarrantyStatusBadge = (warrantyDate)=>{\n        const today = new Date();\n        const warranty = new Date(warrantyDate);\n        const daysUntilExpiry = Math.ceil((warranty.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        if (daysUntilExpiry < 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                variant: \"destructive\",\n                className: \"flex items-center space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Expired\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 14\n            }, this);\n        }\n        if (daysUntilExpiry <= 30) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                variant: \"secondary\",\n                className: \"flex items-center space-x-1 bg-yellow-100 text-yellow-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Expiring Soon\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                lineNumber: 82,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n            variant: \"secondary\",\n            className: \"flex items-center space-x-1 bg-green-100 text-green-800\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: \"Active\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n            lineNumber: 87,\n            columnNumber: 12\n        }, this);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-IN');\n    };\n    const filteredComponents = components.filter((component)=>{\n        const matchesSearch = searchTerm === '' || component.serialNumber.toLowerCase().includes(searchTerm.toLowerCase()) || component.machine.serialNumber.toLowerCase().includes(searchTerm.toLowerCase()) || component.machine.warranty.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) || component.machine.product.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const warrantyDate = new Date(component.warrantyDate);\n        const today = new Date();\n        const isExpired = warrantyDate < today;\n        const isExpiring = !isExpired && warrantyDate <= new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);\n        const isActive = !isExpired && !isExpiring;\n        let matchesStatus = true;\n        if (statusFilter === 'active') matchesStatus = isActive;\n        else if (statusFilter === 'expiring') matchesStatus = isExpiring;\n        else if (statusFilter === 'expired') matchesStatus = isExpired;\n        return matchesSearch && matchesStatus;\n    });\n    const handleExport = async ()=>{\n        try {\n            const response = await fetch('/api/warranties/components/export?format=CSV', {\n                credentials: 'include',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to export component data');\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"warranty-components-\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n        } catch (error) {\n            console.error('Error exporting component data:', error);\n            setError('Failed to export component data');\n        }\n    };\n    // Handle component operations\n    const handleEdit = (component)=>{\n        setEditingComponent(component);\n        setShowEditDialog(true);\n    };\n    const handleDelete = async (componentId)=>{\n        if (!confirm('Are you sure you want to delete this component?')) {\n            return;\n        }\n        try {\n            setDeletingId(componentId);\n            const response = await fetch(\"/api/warranties/components/\".concat(componentId), {\n                method: 'DELETE',\n                credentials: 'include'\n            });\n            if (!response.ok) {\n                throw new Error('Failed to delete component');\n            }\n            // Remove from local state and reload\n            setComponents((prev)=>prev.filter((c)=>c.id !== componentId));\n        } catch (error) {\n            console.error('Error deleting component:', error);\n            setError('Failed to delete component');\n        } finally{\n            setDeletingId(null);\n        }\n    };\n    const handleFormSuccess = ()=>{\n        setShowAddDialog(false);\n        setShowEditDialog(false);\n        setEditingComponent(null);\n        // Reload components\n        window.location.reload();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"pb-3 flex flex-row items-center justify-between bg-primary text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Component Tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"text-gray-100\",\n                                        children: \"Track individual components and their warranty status across all machines\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"secondary\",\n                                        onClick: handleExport,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Export\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"secondary\",\n                                        onClick: ()=>setShowAddDialog(true),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Add Component\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"search\",\n                                                className: \"text-black\",\n                                                children: \"Search\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"search\",\n                                                        placeholder: \"Search by component, machine, or customer...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-10\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"status\",\n                                                className: \"text-black\",\n                                                children: \"Warranty Status\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: statusFilter,\n                                                onValueChange: setStatusFilter,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        id: \"status\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                            placeholder: \"Select status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"all\",\n                                                                children: \"All Statuses\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"active\",\n                                                                children: \"Active\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"expiring\",\n                                                                children: \"Expiring Soon\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: \"expired\",\n                                                                children: \"Expired\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"machine\",\n                                                className: \"text-black\",\n                                                children: \"Machine\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: machineFilter,\n                                                onValueChange: setMachineFilter,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        id: \"machine\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                            placeholder: \"Select machine\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                            value: \"all\",\n                                                            children: \"All Machines\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                                        className: \"text-black\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        className: \"text-black\",\n                                                        children: \"Component\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        className: \"text-black\",\n                                                        children: \"Machine\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        className: \"text-black\",\n                                                        children: \"Customer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        className: \"text-black\",\n                                                        children: \"Product\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        className: \"text-black\",\n                                                        children: \"Warranty Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        className: \"text-black\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        className: \"text-right text-black\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                            children: isLoading ? // Loading skeleton\n                                            Array.from({\n                                                length: 5\n                                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                className: \"h-6 w-24\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                className: \"h-6 w-32\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                className: \"h-6 w-32\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                className: \"h-6 w-28\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                className: \"h-6 w-24\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                className: \"h-6 w-20\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                className: \"h-6 w-16 ml-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 57\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, \"skeleton-\".concat(index), true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 36\n                                                }, this)) : filteredComponents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    colSpan: 7,\n                                                    className: \"text-center py-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-8 w-8 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500\",\n                                                                children: \"No components found\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: ()=>setShowAddDialog(true),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Add First Component\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 70\n                                            }, this) : filteredComponents.map((component)=>{\n                                                var _component_machine, _component_machine1, _component_machine_warranty_customer, _component_machine_warranty, _component_machine2, _component_machine_warranty_customer1, _component_machine_warranty1, _component_machine3, _component_machine_warranty2, _component_machine4, _component_machine_product, _component_machine5, _component_machine_model, _component_machine6, _component_machine_brand, _component_machine7;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            className: \"text-black\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            \"#\",\n                                                                            component.componentNo\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            \"SN: \",\n                                                                            component.serialNumber\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            \"Section: \",\n                                                                            component.section\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            className: \"text-black\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: ((_component_machine = component.machine) === null || _component_machine === void 0 ? void 0 : _component_machine.serialNumber) || 'Unknown Serial'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: ((_component_machine1 = component.machine) === null || _component_machine1 === void 0 ? void 0 : _component_machine1.location) || 'Unknown Location'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 285,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            className: \"text-black\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: ((_component_machine2 = component.machine) === null || _component_machine2 === void 0 ? void 0 : (_component_machine_warranty = _component_machine2.warranty) === null || _component_machine_warranty === void 0 ? void 0 : (_component_machine_warranty_customer = _component_machine_warranty.customer) === null || _component_machine_warranty_customer === void 0 ? void 0 : _component_machine_warranty_customer.name) || 'Unknown Customer'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: ((_component_machine3 = component.machine) === null || _component_machine3 === void 0 ? void 0 : (_component_machine_warranty1 = _component_machine3.warranty) === null || _component_machine_warranty1 === void 0 ? void 0 : (_component_machine_warranty_customer1 = _component_machine_warranty1.customer) === null || _component_machine_warranty_customer1 === void 0 ? void 0 : _component_machine_warranty_customer1.city) || 'Unknown City'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            \"BSL: \",\n                                                                            ((_component_machine4 = component.machine) === null || _component_machine4 === void 0 ? void 0 : (_component_machine_warranty2 = _component_machine4.warranty) === null || _component_machine_warranty2 === void 0 ? void 0 : _component_machine_warranty2.bslNo) || 'N/A'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            className: \"text-black\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: ((_component_machine5 = component.machine) === null || _component_machine5 === void 0 ? void 0 : (_component_machine_product = _component_machine5.product) === null || _component_machine_product === void 0 ? void 0 : _component_machine_product.name) || 'Unknown Product'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: ((_component_machine6 = component.machine) === null || _component_machine6 === void 0 ? void 0 : (_component_machine_model = _component_machine6.model) === null || _component_machine_model === void 0 ? void 0 : _component_machine_model.name) || 'Unknown Model'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: ((_component_machine7 = component.machine) === null || _component_machine7 === void 0 ? void 0 : (_component_machine_brand = _component_machine7.brand) === null || _component_machine_brand === void 0 ? void 0 : _component_machine_brand.name) || 'Unknown Brand'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            className: \"text-black\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatDate(component.warrantyDate)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: getWarrantyStatusBadge(component.warrantyDate)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-end space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                                                            href: \"/warranties/components/\".concat(component.id),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                                lineNumber: 313,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                            lineNumber: 312,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>handleEdit(component),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"text-destructive hover:text-destructive\",\n                                                                        onClick: ()=>handleDelete(component.id),\n                                                                        disabled: deletingId === component.id,\n                                                                        children: deletingId === component.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-red-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                            lineNumber: 320,\n                                                                            columnNumber: 60\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                            lineNumber: 320,\n                                                                            columnNumber: 146\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, component.id, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 69\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            !isLoading && filteredComponents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Showing \",\n                                        filteredComponents.length,\n                                        \" of \",\n                                        components.length,\n                                        \" components\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 59\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.Dialog, {\n                open: showAddDialog,\n                onOpenChange: setShowAddDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogContent, {\n                    className: \"max-w-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogTitle, {\n                                className: \"text-black\",\n                                children: \"Add New Component\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-black\",\n                                    children: \"Warranty component form will be implemented here.\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-2 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowAddDialog(false),\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: handleFormSuccess,\n                                            children: \"Add Component\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.Dialog, {\n                open: showEditDialog,\n                onOpenChange: setShowEditDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogContent, {\n                    className: \"max-w-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogTitle, {\n                                className: \"text-black\",\n                                children: \"Edit Component\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-black\",\n                                    children: \"Warranty component edit form will be implemented here.\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this),\n                                editingComponent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mt-2\",\n                                    children: [\n                                        \"Editing component: \",\n                                        editingComponent.serialNumber\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 34\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-2 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>{\n                                                setShowEditDialog(false);\n                                                setEditingComponent(null);\n                                            },\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: handleFormSuccess,\n                                            children: \"Update Component\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n                lineNumber: 360,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\components\\\\page.tsx\",\n        lineNumber: 167,\n        columnNumber: 10\n    }, this);\n}\n_s(ComponentTrackingPage, \"OeE3I1HxYl6xc894kD63MuN0z3k=\");\n_c1 = ComponentTrackingPage;\n_s1(ComponentTrackingPage, \"OeE3I1HxYl6xc894kD63MuN0z3k=\");\n_c = ComponentTrackingPage;\nvar _c;\n$RefreshReg$(_c, \"ComponentTrackingPage\");\nvar _c1;\n$RefreshReg$(_c1, \"ComponentTrackingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/warranties/components/page.tsx\n"));

/***/ })

});