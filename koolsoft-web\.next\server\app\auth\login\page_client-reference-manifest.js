globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/auth/login/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/providers/SessionProvider.tsx":{"*":{"id":"(ssr)/./src/components/providers/SessionProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/custom-toaster.tsx":{"*":{"id":"(ssr)/./src/components/ui/custom-toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/login/page.tsx":{"*":{"id":"(ssr)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customers/layout.tsx":{"*":{"id":"(ssr)/./src/app/customers/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customers/page.tsx":{"*":{"id":"(ssr)/./src/app/customers/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/amc/layout.tsx":{"*":{"id":"(ssr)/./src/app/amc/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/amc/service-dates/page.tsx":{"*":{"id":"(ssr)/./src/app/amc/service-dates/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/layout.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/amc/page.tsx":{"*":{"id":"(ssr)/./src/app/amc/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customers/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/customers/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customers/[id]/edit/page.tsx":{"*":{"id":"(ssr)/./src/app/customers/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/visit-cards/layout.tsx":{"*":{"id":"(ssr)/./src/app/visit-cards/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/visit-cards/new/page.tsx":{"*":{"id":"(ssr)/./src/app/visit-cards/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/warranties/layout.tsx":{"*":{"id":"(ssr)/./src/app/warranties/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/warranties/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/warranties/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/warranties/page.tsx":{"*":{"id":"(ssr)/./src/app/warranties/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\components\\providers\\SessionProvider.tsx":{"id":"(app-pages-browser)/./src/components/providers/SessionProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\components\\ui\\custom-toaster.tsx":{"id":"(app-pages-browser)/./src/components/ui/custom-toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\auth\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/auth/login/page.tsx","name":"*","chunks":["app/auth/login/page","static/chunks/app/auth/login/page.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\customers\\layout.tsx":{"id":"(app-pages-browser)/./src/app/customers/layout.tsx","name":"*","chunks":[],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\customers\\page.tsx":{"id":"(app-pages-browser)/./src/app/customers/page.tsx","name":"*","chunks":[],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\amc\\layout.tsx":{"id":"(app-pages-browser)/./src/app/amc/layout.tsx","name":"*","chunks":[],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\amc\\service-dates\\page.tsx":{"id":"(app-pages-browser)/./src/app/amc/service-dates/page.tsx","name":"*","chunks":[],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\dashboard\\layout.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/layout.tsx","name":"*","chunks":[],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\amc\\page.tsx":{"id":"(app-pages-browser)/./src/app/amc/page.tsx","name":"*","chunks":[],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\customers\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/customers/[id]/page.tsx","name":"*","chunks":[],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\customers\\[id]\\edit\\page.tsx":{"id":"(app-pages-browser)/./src/app/customers/[id]/edit/page.tsx","name":"*","chunks":[],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\visit-cards\\layout.tsx":{"id":"(app-pages-browser)/./src/app/visit-cards/layout.tsx","name":"*","chunks":[],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\visit-cards\\new\\page.tsx":{"id":"(app-pages-browser)/./src/app/visit-cards/new/page.tsx","name":"*","chunks":[],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\warranties\\layout.tsx":{"id":"(app-pages-browser)/./src/app/warranties/layout.tsx","name":"*","chunks":[],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\warranties\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/warranties/[id]/page.tsx","name":"*","chunks":[],"async":false},"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\warranties\\page.tsx":{"id":"(app-pages-browser)/./src/app/warranties/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"G:\\projects\\KoolSoft\\koolsoft-web\\src\\":[],"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\auth\\login\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/SessionProvider.tsx":{"*":{"id":"(rsc)/./src/components/providers/SessionProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/custom-toaster.tsx":{"*":{"id":"(rsc)/./src/components/ui/custom-toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/login/page.tsx":{"*":{"id":"(rsc)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customers/layout.tsx":{"*":{"id":"(rsc)/./src/app/customers/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customers/page.tsx":{"*":{"id":"(rsc)/./src/app/customers/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/amc/layout.tsx":{"*":{"id":"(rsc)/./src/app/amc/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/amc/service-dates/page.tsx":{"*":{"id":"(rsc)/./src/app/amc/service-dates/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/layout.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/amc/page.tsx":{"*":{"id":"(rsc)/./src/app/amc/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customers/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/customers/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/customers/[id]/edit/page.tsx":{"*":{"id":"(rsc)/./src/app/customers/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/visit-cards/layout.tsx":{"*":{"id":"(rsc)/./src/app/visit-cards/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/visit-cards/new/page.tsx":{"*":{"id":"(rsc)/./src/app/visit-cards/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/warranties/layout.tsx":{"*":{"id":"(rsc)/./src/app/warranties/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/warranties/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/warranties/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/warranties/page.tsx":{"*":{"id":"(rsc)/./src/app/warranties/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}